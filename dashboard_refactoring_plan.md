# Dashboard Refactoring Plan - DataHero4

## 🎯 Objetivo

Simplificar e otimizar a arquitetura do dashboard, eliminando inconsistências, removendo código duplicado e melhorando performance.

## 📋 Decisões Arquiteturais

### 1. Estratégia Escolhida: **Snapshot-First Enhanced**

#### Justificativa
- **Performance**: 19ms vs 10-30s é uma diferença significativa
- **Estabilidade**: Sistema de snapshots já está em produção
- **Escalabilidade**: Mais fácil escalar pré-cálculos que cálculos em tempo real

#### Implementação
- Manter sistema de snapshots como base
- Adicionar flexibilidade de filtros
- Implementar cache inteligente para dados não-snapshot

### 2. Arquitetura de Hooks: **Single Hook Pattern**

#### Decisão
- Manter apenas `useDynamicKpis` como hook principal
- Remover `useKpiData` e `useKpiDataTransition`
- Adaptar `useDynamicKpis` para usar snapshots quando disponível

### 3. API Strategy: **Unified Endpoint**

#### Decisão
- Manter `/api/dashboard/snapshot` como endpoint principal
- Deprecar `/api/dashboard/kpis` gradualmente
- Implementar fallback inteligente para dados não-snapshot

## 🗂️ Estrutura de Arquivos Pós-Refatoração

```
apps/frontend/src/
├── components/dashboard/
│   ├── Dashboard.tsx                    # ✅ Simplificado
│   ├── KpiBentoGrid.tsx                # ✅ Mantido
│   ├── KpiBentoCard.tsx                # ✅ Otimizado
│   ├── DashboardControls.tsx           # ✅ Expandido
│   └── LoadingStates.tsx               # ✅ Mantido
├── hooks/
│   ├── useDashboardData.ts             # 🆕 Hook unificado
│   ├── useDashboardFilters.ts          # ✅ Simplificado
│   ├── [REMOVED] useKpiData.ts         # ❌ Removido
│   ├── [REMOVED] useDynamicKpis.ts     # ❌ Removido
│   └── [REMOVED] useKpiDataTransition.ts # ❌ Removido
├── types/
│   ├── dashboard.ts                    # ✅ Unificado e limpo
│   └── [REMOVED] kpi.ts                # ❌ Merged into dashboard.ts
└── lib/
    ├── api.ts                          # ✅ Simplificado
    └── [REMOVED] services/api.ts       # ❌ Merged into lib/api.ts
```

```
apps/backend/src/
├── api/
│   ├── dashboard_unified.py            # 🆕 API unificada
│   ├── [REMOVED] dashboard_snapshot.py # ❌ Merged
│   └── [REMOVED] interfaces/dashboard_api.py # ❌ Merged
├── services/
│   ├── dashboard_service.py            # 🆕 Serviço unificado
│   ├── [REMOVED] kpi_service.py        # ❌ Merged
│   └── [REMOVED] snapshot_service.py   # ❌ Merged
├── models/
│   ├── dashboard_models.py             # 🆕 Modelos unificados
│   ├── [REMOVED] kpi_models.py         # ❌ Merged
│   └── [REMOVED] snapshot_model.py     # ❌ Merged
└── caching/
    ├── dashboard_cache.py              # 🆕 Cache específico
    └── [REMOVED] unified_cache_system.py # ❌ Simplified
```

## 🔄 Plano de Implementação

### Fase 1: Preparação e Limpeza (2 dias)

#### 1.1 Backup e Testes
```bash
# Criar branch de refatoração
git checkout -b refactor/dashboard-unification

# Executar todos os testes atuais
npm run test
npm run test:backend

# Documentar comportamento atual
npm run test -- --coverage
```

#### 1.2 Análise de Dependências
```bash
# Mapear todas as importações
grep -r "useKpiData\|useDynamicKpis\|useKpiDataTransition" apps/frontend/src/
grep -r "KpiService\|SnapshotService" apps/backend/src/
```

#### 1.3 Criar Tipos Unificados
```typescript
// apps/frontend/src/types/dashboard.ts
export interface KpiData {
  id: string;
  name: string;                    // Padronizado
  description: string;
  currentValue: number;            // Padronizado
  previousValue?: number;
  changePercent?: number;
  trend: 'up' | 'down' | 'stable';
  format: 'currency' | 'percentage' | 'number';
  chartData: ChartDataPoint[];
  category: string;
  isPriority: boolean;
  unit?: string;
  lastUpdated: string;
}

export interface DashboardFilters {
  timeframe: 'day' | 'week' | 'month' | 'quarter';
  currency: 'all' | 'usd' | 'eur' | 'gbp';
  category?: string;
  priorityOnly?: boolean;
}

export interface DashboardState {
  kpis: KpiData[];
  filters: DashboardFilters;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string;
  metadata: DashboardMetadata;
}
```

### Fase 2: Backend Unification (3 dias)

#### 2.1 Criar Serviço Unificado
```python
# apps/backend/src/services/dashboard_service.py
class DashboardService:
    """Serviço unificado para dashboard com snapshot-first strategy."""
    
    def __init__(self):
        self.cache = DashboardCache()
        self.snapshot_manager = SnapshotManager()
        self.kpi_calculator = KpiCalculator()
    
    async def get_dashboard_data(
        self,
        filters: DashboardFilters,
        client_id: str = "L2M"
    ) -> DashboardResponse:
        """
        Estratégia Snapshot-First:
        1. Tentar snapshot se filtros são compatíveis
        2. Calcular dinamicamente se necessário
        3. Cache inteligente para otimização
        """
        # Verificar se pode usar snapshot
        if self._can_use_snapshot(filters):
            snapshot_data = await self._get_snapshot_data(client_id, filters)
            if snapshot_data:
                return snapshot_data
        
        # Fallback para cálculo dinâmico
        return await self._calculate_dynamic_data(filters, client_id)
    
    def _can_use_snapshot(self, filters: DashboardFilters) -> bool:
        """Determina se pode usar dados de snapshot."""
        # Snapshot disponível para filtros padrão
        return (
            filters.timeframe in ['day', 'week'] and
            filters.currency == 'all' and
            not filters.category and
            filters.priorityOnly is not False
        )
```

#### 2.2 Criar API Unificada
```python
# apps/backend/src/api/dashboard_unified.py
@router.get("/api/dashboard/data")
async def get_dashboard_data(
    timeframe: str = Query("week"),
    currency: str = Query("all"),
    category: Optional[str] = Query(None),
    priority_only: bool = Query(True),
    force_refresh: bool = Query(False)
) -> DashboardResponse:
    """
    Endpoint unificado para dados do dashboard.
    
    Estratégia:
    - Snapshot-first para performance
    - Dynamic fallback para flexibilidade
    - Cache inteligente para otimização
    """
    filters = DashboardFilters(
        timeframe=timeframe,
        currency=currency,
        category=category,
        priority_only=priority_only
    )
    
    service = DashboardService()
    
    if force_refresh:
        service.invalidate_cache(filters)
    
    return await service.get_dashboard_data(filters)
```

#### 2.3 Migrar Lógica Existente
```python
# Mover lógica de KpiService e SnapshotService para DashboardService
# Manter compatibilidade com APIs existentes durante transição
# Implementar deprecation warnings
```

### Fase 3: Frontend Unification (2 dias)

#### 3.1 Criar Hook Unificado
```typescript
// apps/frontend/src/hooks/useDashboardData.ts
export const useDashboardData = (initialFilters?: Partial<DashboardFilters>) => {
  const [state, setState] = useState<DashboardState>({
    kpis: [],
    filters: { timeframe: 'week', currency: 'all', ...initialFilters },
    isLoading: true,
    error: null,
    lastUpdated: '',
    metadata: {}
  });

  const updateFilters = useCallback((newFilters: Partial<DashboardFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...newFilters },
      isLoading: true
    }));
  }, []);

  const refreshData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const response = await fetch('/api/dashboard/data?' + new URLSearchParams({
        timeframe: state.filters.timeframe,
        currency: state.filters.currency,
        ...(state.filters.category && { category: state.filters.category }),
        priority_only: state.filters.priorityOnly?.toString() || 'true'
      }));

      if (!response.ok) throw new Error('Failed to fetch dashboard data');
      
      const data = await response.json();
      
      setState(prev => ({
        ...prev,
        kpis: data.kpis,
        isLoading: false,
        lastUpdated: data.lastUpdated,
        metadata: data.metadata
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message
      }));
    }
  }, [state.filters]);

  useEffect(() => {
    refreshData();
  }, [refreshData]);

  return {
    ...state,
    updateFilters,
    refreshData,
    // Computed properties
    priorityKpis: state.kpis.filter(kpi => kpi.isPriority),
    kpisByCategory: groupBy(state.kpis, 'category')
  };
};
```

#### 3.2 Atualizar Dashboard Component
```typescript
// apps/frontend/src/components/dashboard/Dashboard.tsx
const Dashboard = () => {
  const {
    kpis,
    filters,
    isLoading,
    error,
    updateFilters,
    refreshData,
    lastUpdated
  } = useDashboardData();

  // Remover lógica complexa de estado
  // Simplificar renderização
  // Manter apenas lógica de UI

  return (
    <div className="dashboard">
      <DashboardControls
        filters={filters}
        onFiltersChange={updateFilters}
        onRefresh={refreshData}
        lastUpdated={lastUpdated}
      />
      
      {isLoading ? (
        <DashboardLoading />
      ) : error ? (
        <DashboardError error={error} onRetry={refreshData} />
      ) : (
        <KpiBentoGrid kpis={kpis} />
      )}
    </div>
  );
};
```

### Fase 4: Otimização e Limpeza (2 dias)

#### 4.1 Remover Código Obsoleto
```bash
# Remover arquivos obsoletos
rm apps/frontend/src/hooks/useKpiData.ts
rm apps/frontend/src/hooks/useDynamicKpis.ts
rm apps/frontend/src/hooks/useKpiDataTransition.ts
rm apps/frontend/src/types/kpi.ts
rm apps/frontend/src/services/api.ts

rm apps/backend/src/services/kpi_service.py
rm apps/backend/src/services/snapshot_service.py
rm apps/backend/src/api/dashboard_snapshot.py
rm apps/backend/src/interfaces/dashboard_api.py
```

#### 4.2 Atualizar Importações
```bash
# Encontrar e atualizar todas as importações
find apps/frontend/src -name "*.ts" -o -name "*.tsx" | xargs sed -i 's/useKpiData/useDashboardData/g'
find apps/backend/src -name "*.py" | xargs sed -i 's/KpiService/DashboardService/g'
```

#### 4.3 Implementar Funcionalidades Reais
```typescript
// Remover mocks e implementar funcionalidades reais
const calculateTrend = (current: number, previous: number): 'up' | 'down' | 'stable' => {
  if (!previous) return 'stable';
  const change = ((current - previous) / previous) * 100;
  if (change > 5) return 'up';
  if (change < -5) return 'down';
  return 'stable';
};

const generateChartData = async (kpiId: string, timeframe: string): Promise<ChartDataPoint[]> => {
  // Implementação real baseada em dados históricos
  const response = await fetch(`/api/kpis/${kpiId}/history?timeframe=${timeframe}`);
  return response.json();
};
```

### Fase 5: Testes e Validação (2 dias)

#### 5.1 Atualizar Testes Existentes
```typescript
// apps/frontend/src/hooks/__tests__/useDashboardData.test.ts
describe('useDashboardData', () => {
  it('should load dashboard data on mount', async () => {
    const { result } = renderHook(() => useDashboardData());
    
    expect(result.current.isLoading).toBe(true);
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    expect(result.current.kpis).toHaveLength(6);
    expect(result.current.error).toBeNull();
  });

  it('should update filters and refetch data', async () => {
    const { result } = renderHook(() => useDashboardData());
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    act(() => {
      result.current.updateFilters({ timeframe: 'month' });
    });

    expect(result.current.isLoading).toBe(true);
    expect(result.current.filters.timeframe).toBe('month');
  });
});
```

#### 5.2 Testes de Integração
```python
# apps/backend/src/tests/test_dashboard_integration.py
class TestDashboardIntegration:
    async def test_snapshot_first_strategy(self):
        """Testa se snapshot é usado quando possível."""
        service = DashboardService()
        
        # Filtros compatíveis com snapshot
        filters = DashboardFilters(timeframe='week', currency='all')
        result = await service.get_dashboard_data(filters)
        
        assert result.source == 'snapshot'
        assert result.response_time_ms < 100
    
    async def test_dynamic_fallback(self):
        """Testa fallback para cálculo dinâmico."""
        service = DashboardService()
        
        # Filtros não compatíveis com snapshot
        filters = DashboardFilters(timeframe='quarter', category='volume')
        result = await service.get_dashboard_data(filters)
        
        assert result.source == 'dynamic'
        assert len(result.kpis) > 0
```

#### 5.3 Testes E2E
```typescript
// tests/dashboard.spec.ts
test('dashboard loads and displays KPIs', async ({ page }) => {
  await page.goto('/dashboard');
  
  // Verificar loading state
  await expect(page.locator('[data-testid="dashboard-loading"]')).toBeVisible();
  
  // Aguardar carregamento
  await expect(page.locator('[data-testid="kpi-card"]')).toHaveCount(6);
  
  // Verificar filtros
  await page.selectOption('[data-testid="timeframe-filter"]', 'month');
  await expect(page.locator('[data-testid="dashboard-loading"]')).toBeVisible();
  await expect(page.locator('[data-testid="kpi-card"]')).toHaveCount(6);
});
```

## 📊 Métricas de Sucesso

### Performance
- [ ] Tempo de carregamento inicial < 500ms
- [ ] Mudança de filtros < 1s
- [ ] Cache hit rate > 95%
- [ ] Bundle size reduzido em 30%

### Code Quality
- [ ] Cobertura de testes > 90%
- [ ] 0 arquivos duplicados
- [ ] 0 TODOs em produção
- [ ] TypeScript strict mode

### User Experience
- [ ] Loading states consistentes
- [ ] Error handling robusto
- [ ] Filtros responsivos
- [ ] Animações suaves

## 🚨 Riscos e Mitigações

### Risco 1: Breaking Changes
**Mitigação**: Implementar feature flags e rollback plan

### Risco 2: Performance Regression
**Mitigação**: Testes de performance automatizados

### Risco 3: Data Inconsistency
**Mitigação**: Validação rigorosa de dados e testes de integração

## 📅 Timeline

| Fase | Duração | Entregáveis |
|------|---------|-------------|
| 1. Preparação | 2 dias | Tipos unificados, testes baseline |
| 2. Backend | 3 dias | DashboardService, API unificada |
| 3. Frontend | 2 dias | useDashboardData, Dashboard atualizado |
| 4. Limpeza | 2 dias | Código obsoleto removido |
| 5. Testes | 2 dias | Testes atualizados, E2E |
| **Total** | **11 dias** | Dashboard refatorado e otimizado |

## ✅ Checklist de Conclusão

### Backend
- [ ] DashboardService implementado
- [ ] API unificada funcionando
- [ ] Testes de integração passando
- [ ] Código obsoleto removido
- [ ] Performance otimizada

### Frontend
- [ ] useDashboardData implementado
- [ ] Dashboard.tsx simplificado
- [ ] Tipos unificados
- [ ] Testes unitários atualizados
- [ ] Bundle otimizado

### Qualidade
- [ ] Cobertura de testes > 90%
- [ ] 0 warnings TypeScript
- [ ] 0 console.errors
- [ ] Performance targets atingidos
- [ ] Documentação atualizada

---

**Próximo Passo**: Aprovação do plano e início da Fase 1 - Preparação e Limpeza.