"""
Cache Lookup Node - PostgreSQL Integration
==========================================

Node responsible for checking PostgreSQL cache for similar queries.
"""

import logging
from typing import Dict, Any, Optional
from src.utils.learning_db_utils import get_db_manager
from src.utils.component_manager import get_context_preservation_engine
from src.caching.unified_cache_system import get_unified_cache
from src.utils.optimized_logging import get_optimized_logger, ReduceLogging

logger = get_optimized_logger(__name__)

def cache_lookup_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Context-aware cache lookup using unified cache system.

    This node:
    1. Checks unified memory cache first (fastest)
    2. Falls back to PostgreSQL cache (slower)
    3. Processes conversational context ONLY if needed
    """
    try:
        logger.pipeline_step("unified_cache_lookup", "started")

        question = state.get("question", "")
        client_id = state.get("client_id", "")
        sector = state.get("sector", "")
        
        if not question:
            logger.warning("⚠️ No question to lookup")
            return state

        # STEP 1: Try unified cache system
        cache = get_unified_cache()
        cached_result = cache.get(
            "query:result",
            question=question,
            client_id=client_id,
            sector=sector
        )
        
        cache_hit = cached_result is not None
        cache_source = "unified"
        
        # STEP 2: If no cache hit, try PostgreSQL fallback
        if not cache_hit:
            logger.info_debug("No unified cache hit, trying PostgreSQL fallback")
            cached_result, cache_hit = _query_postgresql_cache(question, get_db_manager())
            
            # If we found something in PostgreSQL, cache it in unified cache
            if cache_hit and cached_result:
                cache.set(
                    "query:result",
                    cached_result,
                    question=question,
                    client_id=client_id,
                    sector=sector
                )
                logger.info_debug("Cached PostgreSQL result in unified cache for future requests")
            
        # Update state with cache information
        state["cache_lookup"] = {
            "hit": cache_hit,
            "cached_result": cached_result,
            "source": cache_source
        }
        
        # Set fields that routing function expects
        state["cache_hit"] = cache_hit
        state["cache_source"] = cache_source if cache_hit else None
        
        if cache_hit and cached_result:
            # Calculate feedback-adjusted confidence
            feedback_score = cached_result.get("feedback_score")
            original_confidence = cached_result["confidence"]
            
            if feedback_score is not None:
                # Apply the same feedback adjustment used in the SQL query
                adjusted_confidence = original_confidence * (1 + feedback_score * 0.3)
                # Ensure it doesn't go below 0
                adjusted_confidence = max(0.0, adjusted_confidence)
            else:
                adjusted_confidence = original_confidence
            
            # Add cached query info to state for potential reuse
            state["cached_query"] = cached_result
            state["cache_confidence"] = original_confidence  # Keep original for reference
            state["confidence_score"] = adjusted_confidence  # Use adjusted for pipeline decisions
            
            # Most importantly: set the cached SQL query for reuse
            state["sql_query"] = cached_result["sql_query"]
            state["query_valid"] = True  # Trust cached queries are valid

            # CRITICAL: Set validation fields for cached queries
            # Since query_generation is skipped on cache hit, we need to provide validation fields
            validation_fields_defaults = {
                'sql_valid': True,  # Trust cached queries are valid
                'validation_errors': [],
                'validation_warnings': [],
                'validation_confidence': 1.0,  # High confidence for cached queries
                'validation_type': 'cached',
                'validation_suggestions': []
            }

            # Set validation fields from cache or defaults (reduced logging)
            cached_fields = 0
            default_fields = 0
            for field, default_value in validation_fields_defaults.items():
                if field in cached_result:
                    state[field] = cached_result[field]
                    cached_fields += 1
                else:
                    state[field] = default_value
                    default_fields += 1

            logger.debug(f"Applied {cached_fields} cached + {default_fields} default validation fields")
            logger.validation_summary(state.get('sql_valid', True))
            
            # Check if we have cached business analysis
            if cached_result.get("business_analysis"):
                # CRITICAL: Validate and fix frontend_display type inconsistency
                validated_analysis = _validate_and_fix_business_analysis(cached_result["business_analysis"], question)

                if validated_analysis:
                    state["cached_business_analysis"] = validated_analysis
                    logger.info_debug("Found cached business analysis - can skip Business Analyst")
                else:
                    logger.warning("Cached business analysis failed validation - will regenerate")
            else:
                logger.debug("No business_analysis found in cached_result")
                
            # Check if we have cached visualization data
            if cached_result.get("visualization_data"):
                state["cached_visualization_data"] = cached_result["visualization_data"]
                logger.info_debug("Found cached visualization data")
            elif state.get("cached_business_analysis"):
                # Create minimal visualization data for ultra_fast path
                state["cached_visualization_data"] = {
                    "chartType": "number",
                    "title": "Resultado da Consulta",
                    "data": [],
                    "source": "cache_fallback"
                }
                logger.debug("Created minimal visualization data for ultra_fast path")
            
        logger.pipeline_step("cache_lookup", "completed", hit=cache_hit, source=cache_source)
        
        # STEP 3: Process conversational context ONLY if no cache hit
        # This avoids expensive LLM calls when we have cached results
        if not cache_hit:
            logger.info_debug("No cache hit - processing conversational context")
            enriched_question = _process_conversational_context(state, question)
            logger.debug(f"Context processing: '{question}' → '{enriched_question}'")
            
            # Update state with context information for downstream nodes
            # (The context fields are already set by _process_conversational_context)
        else:
            logger.debug("Cache hit - skipping expensive context processing")
        
        # Debug: Verify state has cached analysis
        if cache_hit and "cached_business_analysis" in state:
            logger.debug("State contains cached_business_analysis")
        elif cache_hit:
            logger.debug("State does NOT contain cached_business_analysis")
            
        # Return state with explicit merge for parallel execution compatibility
        return {**state}

    except Exception as e:
        logger.error(f"❌ Cache lookup node failed: {e}")
        state["cache_lookup"] = {
            "hit": False,
            "error": str(e),
            "source": "postgresql"
        }
        return {**state}


def _process_conversational_context(state: Dict[str, Any], question: str) -> str:
    """
    Process conversational context to enrich the question BEFORE cache lookup.

    This function:
    1. Extracts thread_id and conversation history from state
    2. LOADS persisted context from previous queries
    3. Uses ContextPreservationEngine to process conversational context
    4. Returns enriched question with inherited context (e.g., "vendas")

    Args:
        state: Current pipeline state
        question: Original question

    Returns:
        Enriched question with conversational context
    """
    try:
        # Extract context information from state
        thread_id = state.get("thread_id", "default")
        conversation_history = state.get("conversation_messages", [])

        logger.debug(f"Processing conversational context for thread: {thread_id}")

        # CRITICAL: Load persisted context BEFORE processing
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            context_loaded = loop.run_until_complete(
                get_context_preservation_engine().load_persisted_context(thread_id)
            )
            loop.close()

            if context_loaded:
                logger.debug(f"Loaded persisted context for thread: {thread_id}")
            else:
                logger.debug(f"No persisted context found for thread: {thread_id}")

        except Exception as e:
            logger.warning(f"⚠️ Failed to load persisted context: {e}")

        # Process context using ContextPreservationEngine
        context_result = get_context_preservation_engine().process_query(
            thread_id=thread_id,
            query=question,
            conversation_history=conversation_history,
            query_result=None  # No query result yet
        )

        # Extract inherited context
        inherited_context = context_result.inherited_context

        # Build enriched question with context
        enriched_question = question

        # Add business filters (e.g., operation_type = 'VENDA')
        if inherited_context.get("business_filters"):
            business_context = []
            for filter_key, filter_value in inherited_context["business_filters"].items():
                # CRITICAL: Map business_operation_type to tipo_operacao
                if filter_key == "business_operation_type" and filter_value:
                    if filter_value == "VENDA":
                        business_context.append("operações de venda")
                    elif filter_value == "COMPRA":
                        business_context.append("operações de compra")
                elif filter_key == "tipo_operacao" and filter_value:
                    business_context.append(f"operações de {filter_value.lower()}")

            if business_context:
                enriched_question = f"{question} ({', '.join(business_context)})"

        # Add temporal filters if present
        if inherited_context.get("temporal_filters"):
            temporal_context = []
            for filter_key, filter_value in inherited_context["temporal_filters"].items():
                if filter_value:
                    temporal_context.append(f"{filter_key}: {filter_value}")

            if temporal_context:
                enriched_question = f"{enriched_question} [{', '.join(temporal_context)}]"

        # Update state with context information for downstream nodes
        state["conversational_context"] = {
            "thread_id": thread_id,
            "inherited_context": inherited_context,
            "context_applied": context_result.context_applied,
            "confidence_score": context_result.confidence_score
        }

        # CRITICAL: Map context to fields expected by Query Generator
        state["temporal_context"] = inherited_context.get("temporal_filters", {})
        state["business_context"] = inherited_context.get("business_filters", {})
        state["filter_context"] = inherited_context.get("active_conditions", {})
        state["resolved_references"] = {}  # Will be populated by entity resolution

        logger.debug(f"Mapped context for Query Generator: temporal={len(state['temporal_context'])}, business={len(state['business_context'])}")

        logger.debug(f"Context enrichment: confidence={context_result.confidence_score:.2f}, applied={context_result.context_applied}")

        return enriched_question

    except Exception as e:
        logger.warning(f"⚠️ Context processing failed: {e}")
        return question  # Return original question if context processing fails


def _query_postgresql_cache(question: str, db_manager) -> tuple[Optional[Dict[str, Any]], bool]:
    """
    Query PostgreSQL cache directly (L3 cache).
    
    Returns:
        Tuple of (cached_result, cache_hit)
    """
    try:
        with db_manager.get_session() as session:
            from sqlalchemy import text
            
            # Try exact match first (fastest)
            similar_query = session.execute(text("""
                SELECT id, question, sql_query, confidence, result_count,
                       execution_time, entities, last_used, use_count,
                       feedback_score, positive_feedback_count, negative_feedback_count,
                       last_feedback_date, business_analysis, visualization_data
                FROM query_cache
                WHERE LOWER(TRIM(question)) = LOWER(TRIM(:exact_question))
                AND confidence > 0.7
                AND COALESCE(feedback_score, 0) > -0.9
                ORDER BY confidence DESC, created_at DESC
                LIMIT 1
            """), {
                "exact_question": question.strip()
            }).fetchone()

            # If no exact match, try keyword-based fuzzy matching
            if not similar_query:
                key_words = [word for word in question.lower().split() 
                            if len(word) > 3 and word not in ['quanto', 'quantas', 'qual', 'quais', 'como', 'onde', 'quando', 'para', 'pela', 'pelo', 'pela']]
                
                if key_words:
                    ilike_patterns = [f"LOWER(question) ILIKE '%{word}%'" for word in key_words[:3]]
                    ilike_condition = " AND ".join(ilike_patterns)
                    
                    fuzzy_query = session.execute(text(f"""
                        SELECT id, question, sql_query, confidence, result_count,
                               execution_time, entities, last_used, use_count,
                               feedback_score, positive_feedback_count, negative_feedback_count,
                               last_feedback_date, business_analysis, visualization_data
                        FROM query_cache
                        WHERE {ilike_condition}
                        AND confidence > 0.85
                        AND COALESCE(feedback_score, 0) > -0.5
                        AND LENGTH(question) BETWEEN :min_len AND :max_len
                        ORDER BY confidence DESC, created_at DESC
                        LIMIT 1
                    """), {
                        "min_len": len(question) * 0.7,
                        "max_len": len(question) * 1.3
                    }).fetchone()
                    
                    if fuzzy_query:
                        logger.debug(f"PostgreSQL fuzzy cache hit using keywords: {key_words}")
                        similar_query = fuzzy_query
            
            if similar_query:
                cached_result = {
                    "id": similar_query[0],
                    "question": similar_query[1], 
                    "sql_query": similar_query[2],
                    "confidence": float(similar_query[3]),
                    "result_count": similar_query[4],
                    "execution_time": similar_query[5],
                    "entities": similar_query[6] or {},
                    "last_used": similar_query[7],
                    "use_count": similar_query[8],
                    "feedback_score": similar_query[9],
                    "positive_feedback_count": similar_query[10] or 0,
                    "negative_feedback_count": similar_query[11] or 0,
                    "last_feedback_date": similar_query[12],
                    "business_analysis": similar_query[13],
                    "visualization_data": similar_query[14]
                }
                
                # Update usage count
                session.execute(text("""
                    UPDATE query_cache 
                    SET use_count = use_count + 1, last_used = CURRENT_TIMESTAMP
                    WHERE id = :cache_id
                """), {"cache_id": similar_query[0]})
                
                feedback_info = ""
                if cached_result.get("feedback_score") is not None:
                    feedback_info = f" (feedback: {cached_result['feedback_score']:.2f}, +{cached_result['positive_feedback_count']}/-{cached_result['negative_feedback_count']})"
                logger.info(f"PostgreSQL cache hit! Confidence {cached_result['confidence']:.2f}{feedback_info}")
                
                return cached_result, True
            
            return None, False
            
    except Exception as e:
        logger.warning(f"❌ PostgreSQL cache lookup failed: {e}")
        return None, False


def _validate_and_fix_business_analysis(business_analysis: Dict[str, Any], question: str) -> Optional[Dict[str, Any]]:
    """
    Validate and fix business_analysis structure, especially frontend_display type inconsistency.

    This function handles the migration from old cache entries where frontend_display was a string
    to the new structure where it should be a dictionary with question_summary, direct_answer,
    and detailed_explanation.

    Args:
        business_analysis: Cached business analysis data
        question: Original question for context

    Returns:
        Fixed business analysis or None if unfixable
    """
    try:
        if not isinstance(business_analysis, dict):
            logger.warning(f"❌ business_analysis is not a dict: {type(business_analysis)}")
            return None

        # Create a copy to avoid modifying the original
        fixed_analysis = business_analysis.copy()

        # Check for any valid response field
        frontend_display = fixed_analysis.get("frontend_display")
        direct_answer = fixed_analysis.get("direct_answer")

        # If we already have direct_answer, we're good
        if direct_answer:
            logger.debug("Found direct_answer in cached business_analysis")
            fixed_analysis["analysis_level"] = "direct_answer"
            return fixed_analysis

        # Extract direct_answer before cleaning legacy fields
        direct_answer = None

        # Try to extract direct_answer from various sources
        if "direct_answer" in fixed_analysis:
            direct_answer = fixed_analysis["direct_answer"]
        elif isinstance(frontend_display, dict) and "direct_answer" in frontend_display:
            direct_answer = frontend_display["direct_answer"]
        elif isinstance(frontend_display, str):
            direct_answer = frontend_display
        elif "executive_summary" in fixed_analysis:
            direct_answer = fixed_analysis["executive_summary"]

        if not direct_answer:
            logger.warning("❌ Could not extract direct_answer from cached analysis")
            return None

        # Legacy cleanup - remove all old format fields
        legacy_fields = ["frontend_display", "executive_summary", "key_findings",
                        "business_implications", "strategic_recommendations",
                        "next_actions", "risk_factors"]

        for field in legacy_fields:
            if field in fixed_analysis:
                del fixed_analysis[field]

        # Ensure progressive disclosure format
        fixed_analysis["direct_answer"] = direct_answer
        fixed_analysis["analysis_level"] = "direct_answer"

        logger.debug(f"Successfully validated cached business analysis with direct_answer: {direct_answer[:50]}...")

        return fixed_analysis

    except Exception as e:
        logger.error(f"❌ Error validating business_analysis: {e}")
        return None


