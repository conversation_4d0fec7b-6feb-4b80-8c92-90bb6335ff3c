/**
 * Test Utilities
 * 
 * Custom render functions and utilities for testing React components
 * with proper providers and realistic test data.
 */

import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  // Create a new QueryClient for each test to avoid state leakage
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false, // Disable retries in tests
        gcTime: 0, // Disable caching in tests
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Custom matchers and utilities
export const waitForLoadingToFinish = async () => {
  // Wait for any loading states to complete
  await new Promise(resolve => setTimeout(resolve, 0));
};

export const mockConsoleError = () => {
  const originalError = console.error;
  const mockError = vi.fn();
  console.error = mockError;

  return {
    mockError,
    restore: () => {
      console.error = originalError;
    }
  };
};

export const mockConsoleLog = () => {
  const originalLog = console.log;
  const mockLog = vi.fn();
  console.log = mockLog;

  return {
    mockLog,
    restore: () => {
      console.log = originalLog;
    }
  };
};

// Test data generators
export const generateMockKpi = (overrides = {}) => ({
  id: 'test_kpi',
  name: 'Test KPI',
  description: 'A test KPI for unit testing',
  category: 'test',
  priority: 'medium' as const,
  format_type: 'number' as const,
  chart_type: 'line' as const,
  display_order: 1,
  is_priority: false,
  frequency: 'daily',
  cache_ttl: 300,
  currentValue: 1000,
  trend: 'up' as const,
  chartData: [
    { name: 'Day 1', value: 800 },
    { name: 'Day 2', value: 900 },
    { name: 'Day 3', value: 1000 },
  ],
  calculatedAt: new Date().toISOString(),
  ...overrides,
});

export const generateMockFilters = (overrides = {}) => ({
  timeframe: 'week',
  currency: 'all',
  sector: 'cambio',
  client_id: 'L2M',
  priority_only: true,
  ...overrides,
});

export const generateMockFilterDefinition = (overrides = {}) => ({
  key: 'timeframe',
  name: 'Período',
  type: 'select' as const,
  options: [
    { value: '1d', label: '1 Dia', isDefault: false },
    { value: 'week', label: '1 Semana', isDefault: true },
    { value: 'month', label: '1 Mês', isDefault: false },
  ],
  defaultValue: 'week',
  required: true,
  ...overrides,
});

// Mock API responses
export const mockApiResponse = <T>(data: T, overrides = {}) => ({
  data,
  success: true,
  timestamp: new Date().toISOString(),
  metadata: {
    total_count: Array.isArray(data) ? data.length : 1,
    execution_time_ms: 50,
  },
  ...overrides,
});

// Custom hooks for testing
export const renderHookWithProviders = <T>(hook: () => T) => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <AllTheProviders>{children}</AllTheProviders>
  );
  
  return { wrapper };
};

// Assertion helpers
export const expectToBeLoading = (element: HTMLElement) => {
  expect(element).toHaveAttribute('aria-busy', 'true');
};

export const expectNotToBeLoading = (element: HTMLElement) => {
  expect(element).not.toHaveAttribute('aria-busy', 'true');
};

export const expectToHaveError = (element: HTMLElement, errorMessage?: string) => {
  expect(element).toHaveAttribute('role', 'alert');
  if (errorMessage) {
    expect(element).toHaveTextContent(errorMessage);
  }
};

// Mock fetch for API calls
export const mockFetch = (response: any, options: { ok?: boolean; status?: number } = {}) => {
  const mockResponse = {
    ok: options.ok ?? true,
    status: options.status ?? 200,
    json: async () => response,
    text: async () => JSON.stringify(response),
  };

  global.fetch = vi.fn().mockResolvedValue(mockResponse);
  return global.fetch as any;
};

export const mockFetchError = (error: string, status = 500) => {
  const mockResponse = {
    ok: false,
    status,
    statusText: error,
    json: async () => ({ error, success: false }),
  };

  global.fetch = vi.fn().mockResolvedValue(mockResponse);
  return global.fetch as any;
};

// Cleanup utilities
export const cleanupMocks = () => {
  vi.clearAllMocks();
  if (global.fetch && vi.isMockFunction(global.fetch)) {
    global.fetch.mockClear();
  }
};

// Test environment setup
export const setupTestEnvironment = () => {
  // Only setup if we're in a test environment with global objects
  if (typeof global !== 'undefined') {
    // Mock window.matchMedia for responsive tests
    if (typeof window !== 'undefined') {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });
    }

    // Mock IntersectionObserver
    global.IntersectionObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));

    // Mock ResizeObserver
    global.ResizeObserver = vi.fn().mockImplementation(() => ({
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    }));
  }
};

// Call setup on import only in test environment
if (process.env.NODE_ENV === 'test' || process.env.VITEST) {
  setupTestEnvironment();
}
