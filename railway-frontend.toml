# Railway configuration for DataHero4 Frontend
# Separate service configuration for frontend deployment

[build]
builder = "dockerfile"
dockerfilePath = "apps/frontend/Dockerfile"
buildCommand = "echo 'Building DataHero4 frontend...'"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

# Environment variables for production
[variables]
NODE_ENV = "production"
VITE_API_URL = "https://datahero4-backend-production.up.railway.app"
VITE_API_BASE_URL = "https://datahero4-backend-production.up.railway.app"