#!/usr/bin/env zsh
# -*- coding: utf-8 -*-
# scripts/dev.sh
# --------------------------------------------------
# Inicia servidores de backend e frontend usando Turborepo em modo
# de desenvolvimento. Garante que dependências estejam instaladas.
# --------------------------------------------------
# Requisitos:
#   * Node >= 18
#   * Poetry >= 1.7
#   * Turbo (será instalado automaticamente se não existir)
# --------------------------------------------------

# Força o uso do zsh se não estiver sendo executado com zsh
if [ -z "${ZSH_VERSION:-}" ]; then
  exec zsh "$0" "$@"
fi

set -euo pipefail

printf "\n🚀  Iniciando ambiente de desenvolvimento DataHero4 Monorepo...\n\n"

# Verifica Turbo
if ! command -v turbo &> /dev/null; then
  printf "❌  Turbo não encontrado. Instalando...\n"
  npm install -g turbo >/dev/null 2>&1
fi

# Instala dependências root
if [[ ! -d "node_modules" ]]; then
  printf "📦  Instalando dependências do workspace...\n"
  npm install
fi

# Instala dependências backend
if [[ ! -d "apps/backend/.venv" ]]; then
  printf "🐍  Instalando dependências do backend...\n"
  pushd apps/backend >/dev/null
  /opt/homebrew/bin/poetry install
  popd >/dev/null
fi

# Instala dependências frontend
if [[ ! -d "apps/frontend/node_modules" ]]; then
  printf "⚛️  Instalando dependências do frontend...\n"
  pushd apps/frontend >/dev/null
  npm install
  popd >/dev/null
fi

printf "✅  Dependências prontas. Iniciando servidores de desenvolvimento...\n\n"

# Função para cleanup ao sair
cleanup() {
  printf "\n🛑  Parando servidores...\n"
  kill $(jobs -p) 2>/dev/null
  exit 0
}

# Captura sinais para cleanup
trap cleanup SIGINT SIGTERM

# Inicia backend em background com prefixo nos logs e logging verboso
printf "🐍  Iniciando backend (FastAPI)...\n"
cd apps/backend

# Carrega variáveis de ambiente do .env
if [[ -f ".env" ]]; then
  printf "📋  Carregando variáveis de ambiente do .env...\n"
  export $(grep -v '^#' .env | grep -v '^$' | xargs)
fi

# Configurar logging verboso para desenvolvimento
export LOG_LEVEL=DEBUG
export UVICORN_LOG_LEVEL=debug
export PYTHONUNBUFFERED=1  # Garante que os logs Python não sejam bufferizados
/opt/homebrew/bin/poetry run uvicorn src.interfaces.api:app --reload --host 0.0.0.0 --port 8000 --log-level debug --access-log 2>&1 | sed 's/^/[BACKEND] /' &
BACKEND_PID=$!
cd ../..

# Aguarda um pouco para o backend iniciar
sleep 2

# Inicia frontend em background com prefixo nos logs
printf "⚛️  Iniciando frontend (Vite)...\n"
cd apps/frontend
npm run dev 2>&1 | sed 's/^/[FRONTEND] /' &
FRONTEND_PID=$!
cd ../..

printf "\n🚀  Servidores iniciados:\n"
printf "   • Backend:  http://localhost:8000\n"
printf "   • Frontend: http://localhost:3000\n"
printf "\n📊  Monitorando logs (Ctrl+C para parar)...\n"
printf "   🐍 [BACKEND]  = logs do FastAPI/LangGraph\n"
printf "   ⚛️ [FRONTEND] = logs do Vite/React\n\n"

# Aguarda os processos
wait
