# Railway Deployment Fix Guide

## Problem Summary
The Railway deployment fails because the frontend is not deployed, causing "Unexpected token '<', "<!DOCTYPE "... is not valid JSON" errors when the frontend tries to call API endpoints that don't exist.

## Root Cause
1. **Backend only deployment**: Railway only deploys the backend service
2. **Missing frontend service**: No static file serving for the React frontend
3. **Environment mismatch**: Frontend still points to localhost or Render URLs
4. **API calls fail**: Frontend receives HTML error pages instead of JSON

## Solution Steps

### Step 1: Deploy Frontend as Separate Railway Service

1. **Create new Railway service** for frontend:
   ```bash
   # In Railway dashboard, create new service from GitHub repo
   # Select "apps/frontend" as root directory
   # Use the provided Dockerfile and nginx.conf
   ```

2. **Configure environment variables** in Railway frontend service:
   ```
   NODE_ENV=production
   VITE_API_URL=https://[your-backend-service].up.railway.app
   VITE_API_BASE_URL=https://[your-backend-service].up.railway.app
   ```

3. **Update backend CORS** (already done in webapp_unified.py):
   - Added Railway frontend URLs to allowed origins
   - Includes wildcard for Railway subdomains

### Step 2: Files Created

✅ **Frontend Dockerfile** (`apps/frontend/Dockerfile`)
- Multi-stage build with Node.js and nginx
- Optimized for Railway deployment
- Includes health check endpoint

✅ **Nginx Configuration** (`apps/frontend/nginx.conf`)
- SPA routing support
- Static asset caching
- Security headers
- Health check endpoint

✅ **Railway Environment** (`apps/frontend/.env.railway`)
- Production API URLs
- Railway-specific configuration

✅ **Railway Frontend Config** (`railway-frontend.toml`)
- Frontend service configuration
- Build and deployment settings

✅ **Backend CORS Update** (webapp_unified.py)
- Added Railway frontend URLs
- Wildcard Railway subdomain support

### Step 3: Deployment Process

1. **Deploy Backend Service** (existing):
   ```
   Service: datahero4-backend
   Config: railway.toml
   URL: https://datahero4-backend-production.up.railway.app
   ```

2. **Deploy Frontend Service** (new):
   ```
   Service: datahero4-frontend
   Config: railway-frontend.toml
   URL: https://datahero4-frontend-production.up.railway.app
   ```

3. **Update Environment Variables**:
   - Frontend service: Point VITE_API_URL to backend URL
   - Backend service: Add frontend URL to CORS_ALLOW_ORIGINS

### Step 4: Testing

1. **Health Checks**:
   - Backend: `https://[backend-url]/health`
   - Frontend: `https://[frontend-url]/health`

2. **API Connectivity**:
   - Test: `https://[frontend-url]` should load React app
   - Test: Frontend should successfully call backend APIs
   - Check: Browser devtools for CORS errors

## Expected Results

✅ **Frontend loads properly** from Railway URL
✅ **API calls return JSON** instead of HTML error pages  
✅ **KPIs load successfully** without "Erro ao carregar KPIs"
✅ **Full application functionality** across both services

## Alternative: Single Service Deployment

If you prefer a single service, modify the backend to serve static frontend files:

1. Add frontend build to backend Docker image
2. Configure FastAPI to serve static files
3. Update routing to handle SPA fallback

However, the **separate services approach is recommended** for:
- Better separation of concerns
- Independent scaling
- Easier debugging
- Faster frontend deployments